/**
 * TPA API Type Definitions
 * 
 * TypeScript-style interfaces and type definitions for TPA API requests and responses.
 * These provide type safety and documentation for API data structures.
 */

/**
 * Policy List Search Parameters
 * Must use exactly one of the 10 valid parameter combinations
 */
export const PolicyListParams = {
  // Combination 1: INSURER_CODE + CITIZEN_ID
  INSURER_CITIZEN: ['INSURER_CODE', 'CITIZEN_ID'],

  // Combination 2: INSURER_CODE + POLICY_NO + NAME_TH
  INSURER_POLICY_TH: ['INSURER_CODE', 'POLICY_NO', 'NAME_TH'],

  // Combination 3: INSURER_CODE + POLICY_NO + NAME_EN
  INSURER_POLICY_EN: ['INSURER_CODE', 'POLICY_NO', 'NAME_EN'],

  // Combination 4: INSURER_CODE + CERTIFICATE_NO + NAME_TH
  INSURER_CERT_TH: ['INSURER_CODE', 'CERTIFICATE_NO', 'NAME_TH'],

  // Combination 5: INSURER_CODE + CERTIFICATE_NO + NAME_EN
  INSURER_CERT_EN: ['INSURER_CODE', 'CERTIFICATE_NO', 'NAME_EN'],

  // Combination 6: INSURER_CODE + STAFF_NO + NAME_TH
  INSURER_STAFF_TH: ['INSURER_CODE', 'STAFF_NO', 'NAME_TH'],

  // Combination 7: INSURER_CODE + STAFF_NO + NAME_EN
  INSURER_STAFF_EN: ['INSURER_CODE', 'STAFF_NO', 'NAME_EN'],

  // Combination 8: INSURER_CODE + OTHER_ID
  INSURER_OTHER: ['INSURER_CODE', 'OTHER_ID'],

  // Combination 9: INSURER_CODE + NAME_TH
  INSURER_NAME_TH: ['INSURER_CODE', 'NAME_TH'],

  // Combination 10: INSURER_CODE + NAME_EN
  INSURER_NAME_EN: ['INSURER_CODE', 'NAME_EN']
};

/**
 * Claim List Search Parameters
 * Must use exactly one of the 2 valid parameter combinations
 */
export const ClaimListParams = {
  // Combination 1: MEMBER_CODE
  MEMBER: ['MEMBER_CODE'],

  // Combination 2: INSURER_CODE + CITIZEN_ID
  INSURER_CITIZEN: ['INSURER_CODE', 'CITIZEN_ID']
};

import { loadMemberData, loadInsurerData } from '../utils/csvReader.js';

/**
 * Dynamic valid parameter values loaded from CSV data
 * These are populated asynchronously from CSV files
 */
let dynamicValidValues = null;

/**
 * Load valid values from CSV data
 * @returns {Promise<Object>} Promise resolving to valid values object
 */
async function loadValidValues() {
  if (dynamicValidValues) {
    return dynamicValidValues;
  }

  try {
    const [memberData, insurerData] = await Promise.all([
      loadMemberData(),
      loadInsurerData()
    ]);

    // Extract unique values from CSV data
    const citizenIds = [...new Set(memberData.map(m => m.citizenID))];
    const memberCodes = [...new Set(memberData.map(m => m.memberCode))];
    const insurerCodes = [...new Set(insurerData.map(i => i.insurerCode))];
    const namesTH = [...new Set(memberData.map(m => `${m.nameTH} ${m.surnameTH}`))];
    const namesEN = [...new Set(memberData.map(m => `${m.nameEN} ${m.surnameEN}`))];

    dynamicValidValues = {
      INSURER_CODE: insurerCodes,
      CITIZEN_ID: citizenIds,
      MEMBER_CODE: memberCodes,
      NAME_TH: namesTH,
      NAME_EN: namesEN,
      // Static values that don't come from CSV
      POLICY_NO: [
        'POL001', 'POL002', 'POL003', 'POL004', 'POL005',
        'POL006', 'POL007', 'POL008', 'POL009', 'POL010'
      ],
      CERTIFICATE_NO: [
        'CERT001', 'CERT002', 'CERT003', 'CERT004', 'CERT005', 'CERT006',
        'CERT007', 'CERT008', 'CERT009', 'CERT010', 'CERT011', 'CERT012'
      ],
      STAFF_NO: [
        'ST001', 'ST002', 'ST003', 'ST004', 'ST005', 'ST006',
        'ST007', 'ST008', 'ST009', 'ST010', 'ST011', 'ST012'
      ],
      OTHER_ID: [
        'EMP001', 'EMP002', 'EMP003', 'EMP004', 'EMP005', 'EMP006',
        'EMP007', 'EMP008', 'EMP009', 'EMP010', 'EMP011', 'EMP012'
      ]
    };

    return dynamicValidValues;
  } catch (error) {
    console.error('Error loading valid values from CSV:', error);
    // Return fallback static values
    return getFallbackValidValues();
  }
}

/**
 * Fallback valid values in case CSV loading fails
 * @returns {Object} Static valid values object
 */
function getFallbackValidValues() {
  return {
    INSURER_CODE: ['INS001', 'INS002', 'INS003'],
    CITIZEN_ID: ['1234567890123', '1234567890124', '1234567890125'],
    MEMBER_CODE: ['MEM001', 'MEM002', 'MEM003', 'MEM004', 'MEM005', 'MEM006', 'MEM007', 'MEM008'],
    NAME_TH: ['สมชาย ใจดี', 'มาลี สวยงาม', 'ปิยะดา สุขใส'],
    NAME_EN: ['Somchai Jaidee', 'Malee Suayngam', 'Piyada Suksai'],
    POLICY_NO: ['POL001', 'POL002', 'POL003'],
    CERTIFICATE_NO: ['CERT001', 'CERT002', 'CERT003'],
    STAFF_NO: ['ST001', 'ST002', 'ST003'],
    OTHER_ID: ['EMP001', 'EMP002', 'EMP003']
  };
}

/**
 * Get valid values (async version)
 * @returns {Promise<Object>} Promise resolving to valid values
 */
export async function getValidValues() {
  return await loadValidValues();
}

/**
 * Legacy ValidValues export for backward compatibility
 * Note: This is now a static fallback. Use getValidValues() for dynamic data.
 */
export const ValidValues = getFallbackValidValues();

/**
 * Policy status types
 */
export const PolicyStatus = {
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
  EXPIRED: 'Expired',
  PENDING: 'Pending',
  CANCELLED: 'Cancelled'
};

/**
 * Claim status types
 */
export const ClaimStatus = {
  APPROVED: 'Approved',
  AUTHORIZED: 'Authorized',
  OPEN: 'Open',
  PAID: 'Paid',
  PENDING: 'Pending',
  PENDING_APPROVAL: 'Pending For Approval',
  REJECTED: 'Rejected'
};

/**
 * Member types
 */
export const MemberType = {
  PRINCIPAL: 'Principal',
  DEPENDENT: 'Dependent'
};

/**
 * Card types
 */
export const CardType = {
  STANDARD: 'Standard',
  GOLD: 'Gold',
  PLATINUM: 'Platinum',
  DIAMOND: 'Diamond'
};

/**
 * Language preferences
 */
export const Language = {
  TH: 'TH',
  EN: 'EN'
};

/**
 * VIP status
 */
export const VipStatus = {
  YES: 'Y',
  NO: 'N'
};

/**
 * Member status types
 */
export const MemberStatus = {
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
  SUSPENDED: 'Suspended'
};

/**
 * Gender types
 */
export const Gender = {
  MALE: 'M',
  FEMALE: 'F'
};

/**
 * Citizenship types
 */
export const Citizenship = {
  THAI: 'Thai',
  FOREIGN: 'Foreign'
};

/**
 * Member data structure interface (TypeScript-style)
 * Represents the structure of member data from CSV
 */
export const MemberInterface = {
  memberCode: 'string',           // MEM001, MEM002, etc.
  memberStatus: 'string',         // Active, Inactive, Suspended
  titleTH: 'string',              // นาย, นาง, นางสาว
  nameTH: 'string',               // Thai first name
  surnameTH: 'string',            // Thai surname
  titleEN: 'string',              // Mr., Mrs., Ms.
  nameEN: 'string',               // English first name
  surnameEN: 'string',            // English surname
  citizenID: 'string',            // 13-digit citizen ID
  otherID: 'string',              // Employee ID
  staffNo: 'string',              // Staff number
  insurerCardNo: 'string',        // Insurance card number
  insPreviousCardNo: 'string',    // Previous card number
  policyNo: 'string',             // Policy number
  certificateNo: 'string',        // Certificate number
  memberType: 'string',           // Principal, Dependent
  principleMemberCode: 'string',  // Reference to principal member
  principleName: 'string',        // Principal member name
  vip: 'string',                  // Y, N
  vipRemarks: 'string',           // VIP status remarks
  cardType: 'string',             // Standard, Gold, Platinum, Diamond
  language: 'string',             // TH, EN
  insurerCode: 'string',          // INS001, INS002, etc.
  insurerName: 'string',          // Thai insurer name
  insurerNameEN: 'string',        // English insurer name
  companyCode: 'string',          // Company code
  companyName: 'string',          // Thai company name
  companyNameEN: 'string',        // English company name
  birthDate: 'string',            // YYYY-MM-DD format
  gender: 'string',               // M, F
  citizenship: 'string',          // Thai, Foreign
  countryCode: 'string',          // TH, etc.
  planCode: 'string',             // Plan code
  planName: 'string',             // Plan name
  planEffFrom: 'string',          // Plan effective from date
  planEffTo: 'string',            // Plan effective to date
  mobile: 'string',               // Mobile phone number
  email: 'string'                 // Email address
};

/**
 * Type checking utilities
 */
export const TypeCheckers = {
  /**
   * Check if value is a valid insurer code (async version)
   * @param {string} value - Value to check
   * @returns {Promise<boolean>} Promise resolving to whether value is valid
   */
  async isValidInsurerCode(value) {
    const validValues = await getValidValues();
    return validValues.INSURER_CODE.includes(value);
  },

  /**
   * Check if value is a valid citizen ID (async version)
   * @param {string} value - Value to check
   * @returns {Promise<boolean>} Promise resolving to whether value is valid
   */
  async isValidCitizenId(value) {
    const validValues = await getValidValues();
    return validValues.CITIZEN_ID.includes(value);
  },

  /**
   * Check if value is a valid member code (async version)
   * @param {string} value - Value to check
   * @returns {Promise<boolean>} Promise resolving to whether value is valid
   */
  async isValidMemberCode(value) {
    const validValues = await getValidValues();
    return validValues.MEMBER_CODE.includes(value);
  },

  /**
   * Check if value is a valid policy number
   * @param {string} value - Value to check
   * @returns {Promise<boolean>} Promise resolving to whether value is valid
   */
  async isValidPolicyNo(value) {
    const validValues = await getValidValues();
    return validValues.POLICY_NO.includes(value);
  },

  /**
   * Check if value is a valid Thai name (async version)
   * @param {string} value - Value to check
   * @returns {Promise<boolean>} Promise resolving to whether value is valid
   */
  async isValidNameTh(value) {
    const validValues = await getValidValues();
    return validValues.NAME_TH.includes(value);
  },

  /**
   * Check if value is a valid English name (async version)
   * @param {string} value - Value to check
   * @returns {Promise<boolean>} Promise resolving to whether value is valid
   */
  async isValidNameEn(value) {
    const validValues = await getValidValues();
    return validValues.NAME_EN.includes(value);
  },

  /**
   * Check if value is a valid member status
   * @param {string} value - Value to check
   * @returns {boolean} Whether value is valid
   */
  isValidMemberStatus(value) {
    return Object.values(MemberStatus).includes(value);
  },

  /**
   * Check if value is a valid member type
   * @param {string} value - Value to check
   * @returns {boolean} Whether value is valid
   */
  isValidMemberType(value) {
    return Object.values(MemberType).includes(value);
  },

  /**
   * Check if value is a valid VIP status
   * @param {string} value - Value to check
   * @returns {boolean} Whether value is valid
   */
  isValidVipStatus(value) {
    return Object.values(VipStatus).includes(value);
  },

  /**
   * Check if value is a valid card type
   * @param {string} value - Value to check
   * @returns {boolean} Whether value is valid
   */
  isValidCardType(value) {
    return Object.values(CardType).includes(value);
  },

  /**
   * Check if value is a valid language
   * @param {string} value - Value to check
   * @returns {boolean} Whether value is valid
   */
  isValidLanguage(value) {
    return Object.values(Language).includes(value);
  },

  /**
   * Validate complete member object (async version)
   * @param {Object} member - Member object to validate
   * @returns {Promise<Object>} Promise resolving to validation result with isValid boolean and errors array
   */
  async validateMember(member) {
    const errors = [];

    if (!member) {
      return { isValid: false, errors: ['Member object is required'] };
    }

    // Required fields validation
    const requiredFields = ['memberCode', 'memberStatus', 'memberType'];
    for (const field of requiredFields) {
      if (!member[field]) {
        errors.push(`${field} is required`);
      }
    }

    try {
      // Type-specific validations (async)
      if (member.memberCode && !(await this.isValidMemberCode(member.memberCode))) {
        errors.push('Invalid member code');
      }

      if (member.memberStatus && !this.isValidMemberStatus(member.memberStatus)) {
        errors.push('Invalid member status');
      }

      if (member.memberType && !this.isValidMemberType(member.memberType)) {
        errors.push('Invalid member type');
      }

      if (member.vip && !this.isValidVipStatus(member.vip)) {
        errors.push('Invalid VIP status');
      }

      if (member.cardType && !this.isValidCardType(member.cardType)) {
        errors.push('Invalid card type');
      }

      if (member.language && !this.isValidLanguage(member.language)) {
        errors.push('Invalid language');
      }
    } catch (error) {
      console.error('Error during member validation:', error);
      errors.push('Validation error occurred');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

/**
 * Response structure templates for documentation
 */
export const ResponseStructures = {
  PolicyList: {
    data: 'Array<PolicySummary>',
    total: 'number',
    page: 'number',
    limit: 'number'
  },

  PolicyDetail: {
    policy: 'PolicyDetail',
    benefits: 'Array<Benefit>',
    conditions: 'Array<Condition>',
    claims: 'Array<ClaimSummary>'
  },

  ClaimList: {
    data: 'Array<ClaimSummary>',
    total: 'number',
    page: 'number',
    limit: 'number'
  },

  Error: {
    error: 'string',
    message: 'string',
    details: 'object'
  }
};

/**
 * Default values for optional parameters
 */
export const Defaults = {
  PAGE: 1,
  LIMIT: 10,
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
};
