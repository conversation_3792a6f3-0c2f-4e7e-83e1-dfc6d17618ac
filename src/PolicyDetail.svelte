<!--
  PolicyDetail.svelte

  Comprehensive policy detail component that displays detailed policy information.

  Features:
  - Comprehensive policy information display
  - Policy status and key dates section
  - Action buttons for policy management
  - Related documents and claims sections
  - Responsive design with Tailwind CSS utility classes
  - Semantic HTML structure with ARIA labels
  - Back navigation functionality
  - Integration with member selection functionality
  - Thai language interface

  Usage:
  <PolicyDetail on:navigate />
-->

<script>
  import { createEventDispatcher } from "svelte";
  import { policyDetailStore, loadPolicyDetail } from "./stores/dataStore.js";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
  } from "./stores/memberStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";
  import api from "./api/index.js";
  import { getApiCitizenId } from "./utils/memberData.js";

  const dispatch = createEventDispatcher();

  // Policy list data from PolicyList API
  let policyListData = null;

  // Reactive store subscriptions
  $: policyDetail = $policyDetailStore.data;
  $: loading = $policyDetailStore.loading;
  $: error = $policyDetailStore.error;
  $: selectedMember = $selectedMemberStore;
  $: memberDisplayName = $selectedMemberDisplayName;

  // Navigation handler
  function handleBackNavigation() {
    dispatch("navigate", { page: "policy-list" });
  }

  // Mock documents data (would come from API in production)
  const documents = [
    {
      id: "doc-001",
      name: "ใบรับรองกรมธรรม์",
      type: "PDF",
      size: "245 KB",
      date: "2024-06-15",
    },
    {
      id: "doc-002",
      name: "สรุปความคุ้มครอง",
      type: "PDF",
      size: "156 KB",
      date: "2024-06-15",
    },
    {
      id: "doc-003",
      name: "ข้อกำหนดและเงื่อนไข",
      type: "PDF",
      size: "892 KB",
      date: "2024-06-15",
    },
  ];

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Inactive: "bg-gray-100 text-gray-800 border-gray-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Claim status color classes for enhanced display
  const claimStatusColors = {
    Paid: "bg-green-100 text-green-800 border-green-200",
    Approved: "bg-blue-100 text-blue-800 border-blue-200",
    Authorized: "bg-blue-100 text-blue-800 border-blue-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    "Pending For Approval": "bg-yellow-100 text-yellow-800 border-yellow-200",
    Open: "bg-orange-100 text-orange-800 border-orange-200",
    Rejected: "bg-red-100 text-red-800 border-red-200",
  };

  // Claim type color classes
  const claimTypeColors = {
    AUTO: "bg-purple-100 text-purple-800 border-purple-200",
    HEALTH: "bg-teal-100 text-teal-800 border-teal-200",
    LIFE: "bg-indigo-100 text-indigo-800 border-indigo-200",
    PROPERTY: "bg-amber-100 text-amber-800 border-amber-200",
    TRAVEL: "bg-cyan-100 text-cyan-800 border-cyan-200",
    DEFAULT: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Utility functions
  function formatCurrency(amount) {
    if (!amount) return "ไม่ระบุ";
    return new Intl.NumberFormat("th-TH", {
      style: "currency",
      currency: "THB",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  function formatDate(dateString) {
    if (!dateString) return "ไม่ระบุ";
    const date = new Date(dateString);
    return date.toLocaleDateString("th-TH", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  function formatShortDate(dateString) {
    if (!dateString) return "ไม่ระบุ";
    const date = new Date(dateString);
    return date.toLocaleDateString("th-TH", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  // Enhanced claim utility functions
  function getClaimStatusColor(status) {
    return claimStatusColors[status] || claimStatusColors.Pending;
  }

  function getClaimTypeColor(type) {
    const upperType = type?.toUpperCase();
    return claimTypeColors[upperType] || claimTypeColors.DEFAULT;
  }

  function formatClaimStatus(status) {
    const statusMap = {
      Paid: "จ่ายแล้ว",
      Approved: "อนุมัติแล้ว",
      Authorized: "อนุญาตแล้ว",
      Pending: "รอดำเนินการ",
      "Pending For Approval": "รอการอนุมัติ",
      Open: "เปิดอยู่",
      Rejected: "ปฏิเสธ",
    };
    return statusMap[status] || status;
  }

  function formatClaimType(type) {
    const typeMap = {
      AUTO: "รถยนต์",
      HEALTH: "สุขภาพ",
      LIFE: "ชีวิต",
      PROPERTY: "ทรัพย์สิน",
      TRAVEL: "การเดินทาง",
      Repair: "ซ่อมแซม",
      Medical: "การรักษา",
      Accident: "อุบัติเหตุ",
    };
    return typeMap[type] || type || "ไม่ระบุ";
  }

  function formatDateTime(dateString) {
    if (!dateString) return "ไม่ระบุ";
    const date = new Date(dateString);
    return date.toLocaleDateString("th-TH", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // Load policy detail data using selected member's memberCode
  async function loadPolicyDetailData() {
    if (!selectedMember?.memberCode) return;

    try {
      await loadPolicyDetail(selectedMember.memberCode);
      console.log(
        "Policy detail loaded successfully for member:",
        selectedMember.memberCode,
      );
    } catch (error) {
      console.error("Failed to load policy detail:", error);
    }
  }

  // Load policy list data for the policy information section
  async function loadPolicyListData() {
    if (!selectedMember?.insurerCode || !selectedMember?.citizenID) return;

    try {
      // Convert citizen code to API citizen ID for API call
      const apiCitizenId = getApiCitizenId(selectedMember.citizenID);

      // Use the existing API client to fetch policy data
      const result = await api.policies.searchByCitizenId(
        apiCitizenId,
        selectedMember.insurerCode,
      );

      if (result.success && result.data && result.data.length > 0) {
        // Use the first policy from the response
        const policyData = result.data[0];
        policyListData = policyData;
        console.log(
          "Policy list data loaded successfully for member:",
          selectedMember.memberCode,
          policyData,
        );
      } else {
        console.warn(
          "No policy data found for member:",
          selectedMember.memberCode,
        );
        policyListData = null;
      }
    } catch (error) {
      console.error("Failed to load policy list data:", error);
      policyListData = null;
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPolicyDetailData();
    await loadPolicyListData();
  }

  // Watch for selected member changes and load data
  $: if (selectedMember?.memberCode) {
    loadPolicyDetailData();
  }

  // Watch for selected member changes and load policy list data
  $: if (selectedMember?.insurerCode && selectedMember?.citizenID) {
    loadPolicyListData();
  }

  // Use policy detail from API and extract sections
  $: selectedPolicy = policyDetail;
  $: policyDetailList = policyDetail?.ListPolicyDetail || [];
  $: benefitList = policyDetail?.BenefitList || [];
  $: contractConditions = policyDetail?.ListContractCondition || [];
  $: memberConditions = policyDetail?.ListMemberCondition || [];
  $: claimHistory = policyDetail?.ListClaimHistory || [];

  // Action handlers
  function handleEditPolicy() {
    alert("ฟังก์ชันแก้ไขข้อมูลกรมธรรม์จะถูกพัฒนาในอนาคต");
  }

  function handleRenewPolicy() {
    alert("ฟังก์ชันต่ออายุกรมธรรม์จะถูกพัฒนาในอนาคต");
  }

  function handleCancelPolicy() {
    if (confirm("คุณแน่ใจหรือไม่ที่จะยกเลิกกรมธรรม์นี้?")) {
      alert("ฟังก์ชันยกเลิกกรมธรรม์จะถูกพัฒนาในอนาคต");
    }
  }

  function handleDownloadDocument(docId) {
    alert(`ฟังก์ชันดาวน์โหลดเอกสาร ${docId} จะถูกพัฒนาในอนาคต`);
  }

  function handleViewClaim(claimId) {
    alert(`ฟังก์ชันดูรายละเอียดเคลม ${claimId} จะถูกพัฒนาในอนาคต`);
  }
</script>

{#if !selectedMember}
  <!-- No Member Selected State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="กลับไปยังรายการกรมธรรม์"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        กลับไปรายการกรมธรรม์
      </button>
    </div>

    <div class="max-w-4xl mx-auto text-center">
      <div class="text-8xl mb-6" aria-hidden="true">�</div>
      <h1 class="text-4xl font-bold text-gray-900 mb-4">ไม่ได้เลือกสมาชิก</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
        กรุณาเลือกสมาชิกจากเมนูด้านบนเพื่อดูรายละเอียดกรมธรรม์
      </p>
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="ไปยังรายการกรมธรรม์"
      >
        ดูรายการกรมธรรม์
      </button>
    </div>
  </main>
{:else if loading}
  <!-- Loading State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="กลับไปยังรายการกรมธรรม์"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        กลับไปรายการกรมธรรม์
      </button>
    </div>
    <LoadingStates variant="detail" message="กำลังโหลดรายละเอียดกรมธรรม์..." />
  </main>
{:else if error}
  <!-- Error State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="กลับไปยังรายการกรมธรรม์"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        กลับไปรายการกรมธรรม์
      </button>
    </div>
    <ErrorStates
      variant="api"
      {error}
      on:retry={handleRetry}
      on:goBack={handleBackNavigation}
      message="ไม่สามารถโหลดรายละเอียดกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"
    />
  </main>
{:else if !selectedPolicy}
  <!-- Policy Not Found State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="กลับไปยังรายการกรมธรรม์"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        กลับไปรายการกรมธรรม์
      </button>
    </div>
    <ErrorStates
      variant="not-found"
      on:goBack={handleBackNavigation}
      message="ไม่พบข้อมูลกรมธรรม์ที่ร้องขอ"
    />
  </main>
{:else}
  <!-- Policy Detail Content -->
  <main class="min-h-screen bg-gray-50 py-4 px-4 sm:py-8 sm:px-6 lg:px-8">
    <!-- Back Navigation -->
    <div class="max-w-7xl mx-auto mb-6 sm:mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="กลับไปยังรายการกรมธรรม์"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        กลับไปรายการกรมธรรม์
      </button>
    </div>

    <!-- Main Content Container -->
    <div class="max-w-7xl mx-auto">
      <!-- Policy Header -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <div
          class="flex flex-col lg:flex-row lg:items-center lg:justify-between"
        >
          <div class="flex items-center mb-4 lg:mb-0">
            <span class="text-4xl mr-4" aria-hidden="true">📄</span>
            <div>
              <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">
                รายละเอียดกรมธรรม์ประกันภัย
              </h1>
              <p class="text-gray-600">
                สมาชิก: <span class="font-medium">{memberDisplayName}</span>
                {#if selectedMember?.memberCode}
                  <span class="text-sm text-gray-500 ml-2"
                    >(รหัส: {selectedMember.memberCode})</span
                  >
                {/if}
              </p>
            </div>
          </div>
          {#if selectedMember}
            <div class="flex flex-col sm:flex-row sm:items-center gap-3">
              <span
                class="px-4 py-2 rounded-full text-sm font-medium border text-center
                       {statusColors[selectedMember.memberStatus] ||
                  statusColors.Active}"
                aria-label="สถานะสมาชิก: {selectedMember.memberStatus}"
              >
                {selectedMember.memberStatus}
              </span>
              {#if selectedMember.vip === "Y"}
                <span
                  class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200"
                >
                  VIP
                </span>
              {/if}
              {#if selectedMember.cardType && selectedMember.cardType !== "Standard"}
                <span
                  class="px-3 py-1 rounded-full text-xs font-medium
                         {selectedMember.cardType === 'Gold'
                    ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                    : selectedMember.cardType === 'Platinum'
                      ? 'bg-gray-100 text-gray-800 border border-gray-200'
                      : selectedMember.cardType === 'Diamond'
                        ? 'bg-blue-100 text-blue-800 border border-blue-200'
                        : 'bg-gray-100 text-gray-800 border border-gray-200'}"
                >
                  {selectedMember.cardType}
                </span>
              {/if}
            </div>
          {/if}
        </div>
      </div>

      <!-- Responsive Grid Layout -->
      <div
        class="grid gap-6
                  grid-cols-1
                  lg:grid-cols-3
                  xl:grid-cols-4"
      >
        <!-- Main Content Area -->
        <div class="lg:col-span-3 xl:col-span-4 space-y-6">
          <!-- Policy Overview -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              ข้อมูลกรมธรรม์
            </h2>
            <div class="grid gap-4 sm:grid-cols-2">
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  หมายเลขกรมธรรม์
                </div>
                <p class="text-lg font-semibold text-gray-900">
                  {policyListData?.PolicyNo || "ไม่ระบุ"}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  หมายเลขใบรับรอง
                </div>
                <p class="text-lg font-semibold text-gray-900">
                  {policyListData?.CertificateNo || "ไม่ระบุ"}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  แผนประกันภัย
                </div>
                <p class="text-gray-900">
                  {policyListData?.PlanName || "ไม่ระบุ"}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  วันที่มีผลบังคับใช้
                </div>
                <p class="text-gray-900">
                  {formatDate(policyListData?.PlanEffFrom)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  วันที่สิ้นสุด
                </div>
                <p class="text-gray-900">
                  {formatDate(policyListData?.PlanEffTo)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  บริษัทประกันภัย
                </div>
                <p class="text-gray-900">
                  {policyListData?.InsurerName ||
                    policyListData?.InsurerNameEN ||
                    "ไม่ระบุ"}
                </p>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
              <div class="text-sm font-medium text-gray-500 mb-2">
                บริษัทที่ทำงาน
              </div>
              <p class="text-gray-700 leading-relaxed">
                {policyListData?.CompanyName ||
                  policyListData?.CompanyNameEN ||
                  "ไม่ระบุ"}
              </p>
            </div>
          </section>

          <!-- Member Information -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              ข้อมูลสมาชิก
            </h2>
            <div class="grid gap-4 sm:grid-cols-2">
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  ชื่อ-นามสกุล (ไทย)
                </div>
                <p class="text-gray-900">
                  {selectedMember?.titleTH || ""}
                  {selectedMember?.nameTH || ""}
                  {selectedMember?.surnameTH || ""}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  ชื่อ-นามสกุล (อังกฤษ)
                </div>
                <p class="text-gray-900">
                  {selectedMember?.titleEN || ""}
                  {selectedMember?.nameEN || ""}
                  {selectedMember?.surnameEN || ""}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  เลขบัตรประชาชน
                </div>
                <p class="text-gray-900 font-mono">
                  {selectedMember?.citizenID || "ไม่ระบุ"}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  วันเกิด
                </div>
                <p class="text-gray-900">
                  {formatDate(selectedMember?.birthDate)}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">เพศ</div>
                <p class="text-gray-900">
                  {selectedMember?.gender === "M"
                    ? "ชาย"
                    : selectedMember?.gender === "F"
                      ? "หญิง"
                      : "ไม่ระบุ"}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  ประเภทสมาชิก
                </div>
                <p class="text-gray-900">
                  {selectedMember?.memberType === "Principal"
                    ? "ผู้เอาประกันหลัก"
                    : selectedMember?.memberType === "Dependent"
                      ? "ผู้อยู่ในอุปการะ"
                      : selectedMember?.memberType || "ไม่ระบุ"}
                </p>
              </div>
            </div>
          </section>

          <!-- Main Benefits -->
          {#if policyDetailList.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                ความคุ้มครองหลัก
              </h2>
              <div class="space-y-4">
                {#each policyDetailList as benefit}
                  <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex justify-between items-start">
                      <div>
                        <h3 class="font-medium text-gray-900">
                          {benefit.MainBenefit}
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">
                          {benefit.MainBenefitEN}
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                          {benefit.MainPlanLimitDesc}
                        </p>
                      </div>
                      <div class="text-right">
                        <div class="text-lg font-semibold text-blue-600">
                          {formatCurrency(
                            parseInt(benefit.MainPlanAmount) || 0,
                          )}
                        </div>
                        <div class="text-sm text-gray-500">
                          {benefit.MainPlanUnit1}/{benefit.MainPlanUnit2}
                        </div>
                        <div class="text-sm text-green-600 mt-1">
                          คงเหลือ: {formatCurrency(
                            parseInt(benefit.MainPlanBalance) || 0,
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Detailed Benefits -->
          {#if benefitList.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                รายละเอียดความคุ้มครอง
              </h2>
              <div class="space-y-4">
                {#each benefitList as benefit}
                  <div
                    class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div class="grid gap-4 md:grid-cols-2">
                      <div>
                        <h3 class="font-medium text-gray-900">
                          {benefit.BenefitTH}
                        </h3>
                        <p class="text-sm text-gray-600">
                          {benefit.BenefitEN}
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                          {benefit.SubBenefitTH} ({benefit.SubBenefitEN})
                        </p>
                      </div>
                      <div class="space-y-2">
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-500"
                            >วงเงินต่อครั้ง:</span
                          >
                          <span class="text-sm font-medium">
                            {formatCurrency(parseInt(benefit.LimitAmt) || 0)}
                            <!-- {benefit.LimitAmt} -->
                            <!-- {benefit.LimitUnit} -->
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-500">วงเงินต่อปี:</span
                          >
                          <span class="text-sm font-medium">
                            {formatCurrency(parseInt(benefit.ComLimitAmt) || 0)}
                            <!-- {benefit.ComLimitAmt} -->
                            <!-- {benefit.ComLimitUnit} -->
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-500"
                            >คงเหลือต่อปี:</span
                          >
                          <span class="text-sm font-medium text-green-600">
                            {formatCurrency(
                              parseInt(benefit.BalComLimitAmt) || 0,
                            )}
                            <!-- {benefit.BalComLimitAmt} -->
                            <!-- {benefit.BalComLimitUnit} -->
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Contract Conditions -->
          {#if contractConditions.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                เงื่อนไขสัญญา
              </h2>
              <div class="space-y-4">
                {#each contractConditions as condition}
                  <div
                    class="p-4 border border-gray-200 rounded-lg
                           {condition.ConditionType === 'Exclusion'
                      ? 'bg-red-50 border-red-200'
                      : condition.ConditionType === 'Waiting Period'
                        ? 'bg-yellow-50 border-yellow-200'
                        : 'bg-gray-50'}"
                  >
                    <div class="flex justify-between items-start mb-2">
                      <h3 class="font-medium text-gray-900">
                        {condition.ConditionType === "Exclusion"
                          ? "ข้อยกเว้น"
                          : condition.ConditionType === "Waiting Period"
                            ? "ระยะเวลารอคุ้มครอง"
                            : condition.ConditionType}
                      </h3>
                      <span
                        class="px-2 py-1 text-xs rounded-full
                               {condition.ConditionApply === 'Y'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'}"
                      >
                        {condition.ConditionApply === "Y" ? "มีผล" : "ไม่มีผล"}
                      </span>
                    </div>
                    <p class="text-sm text-gray-700 mb-2">
                      {condition.ConditionDetail}
                    </p>
                    <div
                      class="grid gap-2 sm:grid-cols-2 text-xs text-gray-500"
                    >
                      <div>
                        วันที่เริ่มต้น: {formatDate(condition.EffFromDate)}
                      </div>
                      <div>
                        วันที่สิ้นสุด: {formatDate(condition.EffToDate)}
                      </div>
                    </div>
                    {#if condition.Remarks}
                      <p class="text-xs text-gray-500 mt-2 italic">
                        หมายเหตุ: {condition.Remarks}
                      </p>
                    {/if}
                  </div>
                {/each}
              </div>
            </section>
          {/if}

          <!-- Member Conditions -->
          {#if memberConditions.length > 0}
            <section
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                เงื่อนไขเฉพาะสมาชิก
              </h2>
              <div class="space-y-4">
                {#each memberConditions as condition}
                  <div
                    class="p-4 border border-gray-200 rounded-lg
                           {condition.ConditionType === 'Medical History'
                      ? 'bg-blue-50 border-blue-200'
                      : condition.ConditionType === 'Medication'
                        ? 'bg-purple-50 border-purple-200'
                        : 'bg-gray-50'}"
                  >
                    <div class="flex justify-between items-start mb-2">
                      <h3 class="font-medium text-gray-900">
                        {condition.ConditionType === "Medical History"
                          ? "ประวัติการรักษา"
                          : condition.ConditionType === "Medication"
                            ? "การใช้ยา"
                            : condition.ConditionType}
                      </h3>
                      <span
                        class="px-2 py-1 text-xs rounded-full
                               {condition.Action === 'Monitor'
                          ? 'bg-yellow-100 text-yellow-800'
                          : condition.Action === 'Alert'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'}"
                      >
                        {condition.Action === "Monitor"
                          ? "ต้องติดตาม"
                          : condition.Action === "Alert"
                            ? "เฝ้าระวัง"
                            : condition.Action}
                      </span>
                    </div>
                    <p class="text-sm text-gray-700 mb-2">
                      {condition.ConditionDetail}
                    </p>
                    <div
                      class="grid gap-2 sm:grid-cols-2 text-xs text-gray-500"
                    >
                      <div>
                        วันที่เริ่มต้น: {formatDate(condition.EffFromDate)}
                      </div>
                      <div>
                        วันที่สิ้นสุด: {formatDate(condition.EffToDate)}
                      </div>
                    </div>
                    {#if condition.Remarks}
                      <p class="text-xs text-gray-500 mt-2 italic">
                        หมายเหตุ: {condition.Remarks}
                      </p>
                    {/if}
                  </div>
                {/each}
              </div>
            </section>
          {/if}
          <!-- Enhanced Claims History -->
          <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            aria-labelledby="claims-history-heading"
          >
            <div
              class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6"
            >
              <h2
                id="claims-history-heading"
                class="text-xl font-semibold text-gray-900 mb-2 sm:mb-0"
              >
                ประวัติการเคลม
              </h2>
              {#if claimHistory.length > 0}
                <div class="text-sm text-gray-500">
                  ทั้งหมด {claimHistory.length} รายการ
                </div>
              {/if}
            </div>

            {#if claimHistory.length > 0}
              <div class="space-y-4">
                {#each claimHistory as claim, index}
                  <article
                    class="border border-gray-200 rounded-lg p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200"
                    aria-labelledby="claim-{index}-title"
                  >
                    <!-- Claim Header -->
                    <div
                      class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4"
                    >
                      <div class="flex-1 mb-4 lg:mb-0">
                        <div
                          class="flex flex-col sm:flex-row sm:items-center sm:gap-3 mb-2"
                        >
                          <h3
                            id="claim-{index}-title"
                            class="text-lg font-semibold text-gray-900"
                          >
                            เคลมเลขที่ {claim.ClaimNo ||
                              claim.ClaimID ||
                              "ไม่ระบุ"}
                          </h3>
                          <div class="flex flex-wrap gap-2 mt-2 sm:mt-0">
                            <!-- Claim Status Badge -->
                            <span
                              class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border
                                     {getClaimStatusColor(claim.ClaimStatus)}"
                              aria-label="สถานะเคลม: {formatClaimStatus(
                                claim.ClaimStatus,
                              )}"
                            >
                              {formatClaimStatus(claim.ClaimStatus)}
                            </span>
                            <!-- Claim Type Badge -->
                            {#if claim.ClaimType || claim.ServiceType}
                              <span
                                class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border
                                       {getClaimTypeColor(
                                  claim.ClaimType || claim.ServiceType,
                                )}"
                                aria-label="ประเภทเคลม: {formatClaimType(
                                  claim.ClaimType || claim.ServiceType,
                                )}"
                              >
                                {formatClaimType(
                                  claim.ClaimType || claim.ServiceType,
                                )}
                              </span>
                            {/if}
                          </div>
                        </div>

                        <!-- Diagnosis/Description -->
                        <div class="mb-3">
                          <p class="text-gray-700 leading-relaxed">
                            {claim.DiagTH ||
                              claim.DiagEN ||
                              claim.Description ||
                              "ไม่ระบุรายละเอียด"}
                          </p>
                        </div>
                      </div>

                      <!-- Amount Information -->
                      <div class="lg:text-right lg:ml-6">
                        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                          <div>
                            <div class="text-sm text-gray-500 mb-1">
                              จำนวนที่จ่าย
                            </div>
                            <div class="text-xl font-bold text-green-600">
                              {formatCurrency(parseInt(claim.PayableAmt) || 0)}
                            </div>
                          </div>
                          <div>
                            <div class="text-sm text-gray-500 mb-1">
                              จำนวนที่เรียกร้อง
                            </div>
                            <div class="text-lg font-semibold text-gray-900">
                              {formatCurrency(parseInt(claim.IncurredAmt) || 0)}
                            </div>
                          </div>
                          {#if claim.PayableAmt && claim.IncurredAmt && parseInt(claim.PayableAmt) !== parseInt(claim.IncurredAmt)}
                            <div>
                              <div class="text-sm text-gray-500 mb-1">
                                ส่วนต่าง
                              </div>
                              <div class="text-sm font-medium text-red-600">
                                -{formatCurrency(
                                  parseInt(claim.IncurredAmt) -
                                    parseInt(claim.PayableAmt),
                                )}
                              </div>
                            </div>
                          {/if}
                        </div>
                      </div>
                    </div>

                    <!-- Claim Details Grid -->
                    <div
                      class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 text-sm"
                    >
                      <!-- Visit/Service Date -->
                      {#if claim.VisitDate || claim.ServiceDate}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              วันที่เข้ารับบริการ
                            </div>
                            <div class="text-gray-600">
                              {formatShortDate(
                                claim.VisitDate || claim.ServiceDate,
                              )}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Claim Date -->
                      {#if claim.ClaimDate}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              วันที่ยื่นเคลม
                            </div>
                            <div class="text-gray-600">
                              {formatShortDate(claim.ClaimDate)}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Settlement Date -->
                      {#if claim.SettlementDate}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              วันที่จ่ายเงิน
                            </div>
                            <div class="text-gray-600">
                              {formatShortDate(claim.SettlementDate)}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Provider Information -->
                      {#if claim.ProviderTH || claim.ProviderEN || claim.ProviderName}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              สถานพยาบาล/ผู้ให้บริการ
                            </div>
                            <div class="text-gray-600">
                              {claim.ProviderTH ||
                                claim.ProviderEN ||
                                claim.ProviderName ||
                                "ไม่ระบุ"}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Policy Number -->
                      {#if claim.PolicyNo}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              หมายเลขกรมธรรม์
                            </div>
                            <div class="text-gray-600 font-mono">
                              {claim.PolicyNo}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Member Code -->
                      {#if claim.MemberCode}
                        <div class="flex items-start">
                          <svg
                            class="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                            />
                          </svg>
                          <div>
                            <div class="font-medium text-gray-700">
                              รหัสสมาชิก
                            </div>
                            <div class="text-gray-600 font-mono">
                              {claim.MemberCode}
                            </div>
                          </div>
                        </div>
                      {/if}
                    </div>
                  </article>
                {/each}
              </div>
            {:else}
              <div class="text-center py-12">
                <div class="text-6xl mb-4" aria-hidden="true">📋</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  ไม่พบประวัติการเคลม
                </h3>
                <p class="text-gray-500 max-w-md mx-auto">
                  ยังไม่มีประวัติการเคลมสำหรับสมาชิกนี้
                  หรือข้อมูลอาจยังไม่ได้รับการอัปเดต
                </p>
              </div>
            {/if}
          </section>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 xl:col-span-1 space-y-6">
          <!-- Action Buttons -->
          <!-- <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              การดำเนินการ
            </h2>
            <div class="space-y-3">
              <button
                class="w-full inline-flex items-center justify-center px-4 py-2
                       bg-blue-600 hover:bg-blue-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                on:click={handleEditPolicy}
                aria-label="แก้ไขข้อมูลกรมธรรม์"
              >
                <svg
                  class="mr-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                แก้ไขข้อมูล
              </button>

              <button
                class="w-full inline-flex items-center justify-center px-4 py-2
                       bg-green-600 hover:bg-green-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                on:click={handleRenewPolicy}
                aria-label="ต่ออายุกรมธรรม์"
              >
                <svg
                  class="mr-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                ต่ออายุกรมธรรม์
              </button>

              <button
                class="w-full inline-flex items-center justify-center px-4 py-2
                       bg-red-600 hover:bg-red-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                on:click={handleCancelPolicy}
                aria-label="ยกเลิกกรมธรรม์"
              >
                <svg
                  class="mr-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                ยกเลิกกรมธรรม์
              </button>
            </div>
          </section> -->

          <!-- Contact Information -->
          <!-- <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              ข้อมูลติดต่อ
            </h2>
            <div class="space-y-3">
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">
                  เบอร์โทรศัพท์
                </div>
                <p class="text-gray-900">
                  {selectedMember?.mobile || "ไม่ระบุ"}
                </p>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-500 mb-1">อีเมล</div>
                <p class="text-gray-900 break-all">
                  {selectedMember?.email || "ไม่ระบุ"}
                </p>
              </div>
            </div>
          </section> -->

          <!-- Documents -->
          <!-- <section
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              เอกสารกรมธรรม์
            </h2>
            <div class="space-y-3">
              {#each documents as document}
                <div
                  class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-center">
                    <svg
                      class="w-5 h-5 text-red-500 mr-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        {document.name}
                      </p>
                      <p class="text-xs text-gray-500">
                        {document.size} • {formatShortDate(document.date)}
                      </p>
                    </div>
                  </div>
                  <button
                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    on:click={() => handleDownloadDocument(document.id)}
                    aria-label="ดาวน์โหลด {document.name}"
                  >
                    ดาวน์โหลด
                  </button>
                </div>
              {/each}
            </div>
          </section> -->
        </div>
      </div>
    </div>
  </main>
{/if}
