/**
 * Member Store
 * 
 * Centralized state management for member selection and member-related data.
 * Provides reactive stores for current member selection, member list, and
 * session persistence functionality.
 * 
 * Features:
 * - Current selected member state
 * - Member list management
 * - Session persistence (survives page navigation)
 * - Loading and error states
 * - Member validation
 */

import { writable, derived, get } from 'svelte/store';
import {
  getAllMembers,
  getMemberByCode,
  getMemberOptions,
  getDefaultMember,
  getMemberDisplayName,
  getMemberShortName,
  isMemberVip,
  getMemberCardTypeDisplay
} from '../utils/memberData.js';
import { findMemberByCitizenAndInsurer } from '../utils/selectionData.js';

// Session storage keys for persistence
const STORAGE_KEY = 'insurance_portal_selected_member';
const CITIZEN_STORAGE_KEY = 'insurance_portal_selected_citizen';
const INSURER_STORAGE_KEY = 'insurance_portal_selected_insurer';

// Helper function to get from session storage
function getFromSessionStorage() {
  if (typeof window === 'undefined') return null;

  try {
    const stored = sessionStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.warn('Failed to parse stored member data:', error);
    return null;
  }
}

// Helper function to save to session storage
function saveToSessionStorage(memberCode) {
  if (typeof window === 'undefined') return;

  try {
    if (memberCode) {
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify({ memberCode, timestamp: Date.now() }));
    } else {
      sessionStorage.removeItem(STORAGE_KEY);
    }
  } catch (error) {
    console.warn('Failed to save member data to session storage:', error);
  }
}

// Initialize selected member from session storage or default
async function initializeSelectedMember() {
  const stored = getFromSessionStorage();

  if (stored && stored.memberCode) {
    try {
      const member = await getMemberByCode(stored.memberCode);
      if (member) {
        return member;
      }
    } catch (error) {
      console.warn('Error loading stored member:', error);
    }
  }

  // Fallback to default member
  try {
    return await getDefaultMember();
  } catch (error) {
    console.warn('Error loading default member:', error);
    return null;
  }
}

// Initialize selected citizen from session storage or null
function initializeSelectedCitizen() {
  if (typeof window === 'undefined') return null;

  try {
    const stored = sessionStorage.getItem(CITIZEN_STORAGE_KEY);
    if (stored) {
      const citizenData = JSON.parse(stored);
      console.log(`Restored citizen selection from session: ${citizenData.displayName} (${citizenData.citizenID})`);
      return citizenData;
    }
  } catch (error) {
    console.warn('Failed to restore citizen selection from session storage:', error);
  }
  return null;
}

// Initialize selected insurer from session storage or null
function initializeSelectedInsurer() {
  if (typeof window === 'undefined') return null;

  try {
    const stored = sessionStorage.getItem(INSURER_STORAGE_KEY);
    if (stored) {
      const insurerData = JSON.parse(stored);
      console.log(`Restored insurer selection from session: ${insurerData.displayName} (${insurerData.insurerCode})`);
      return insurerData;
    }
  } catch (error) {
    console.warn('Failed to restore insurer selection from session storage:', error);
  }
  return null;
}

// Create member-related stores
export const memberListStore = writable({
  data: [],
  loading: false,
  error: null,
  lastUpdated: null
});

export const selectedMemberStore = writable(null);

// Initialize the selected member store asynchronously
initializeSelectedMember().then(member => {
  selectedMemberStore.set(member);
}).catch(error => {
  console.error('Failed to initialize selected member:', error);
});

// Two-step selection stores
export const selectedCitizenStore = writable(initializeSelectedCitizen());
export const selectedInsurerStore = writable(initializeSelectedInsurer());

// Derived stores for computed values
export const selectedMemberCode = derived(
  selectedMemberStore,
  $selectedMember => $selectedMember?.memberCode || null
);

export const selectedMemberDisplayName = derived(
  selectedMemberStore,
  $selectedMember => $selectedMember ? getMemberDisplayName($selectedMember, 'TH') : ''
);

export const selectedMemberShortName = derived(
  selectedMemberStore,
  $selectedMember => $selectedMember ? getMemberShortName($selectedMember, 'TH') : ''
);

export const isSelectedMemberVip = derived(
  selectedMemberStore,
  $selectedMember => isMemberVip($selectedMember)
);

export const selectedMemberCardType = derived(
  selectedMemberStore,
  $selectedMember => $selectedMember ? getMemberCardTypeDisplay($selectedMember) : ''
);

export const memberOptions = writable([]);

// Load member options asynchronously
async function loadMemberOptions() {
  try {
    const options = await getMemberOptions(false);
    memberOptions.set(options);
  } catch (error) {
    console.error('Error loading member options:', error);
    memberOptions.set([]);
  }
}

// Initialize member options
loadMemberOptions();

// Two-step selection derived stores
export const selectedCitizenID = derived(
  selectedCitizenStore,
  $selectedCitizen => $selectedCitizen?.citizenID || null
);

export const selectedInsurerCode = derived(
  selectedInsurerStore,
  $selectedInsurer => $selectedInsurer?.insurerCode || null
);

// Combined selection state - automatically updates selectedMemberStore when both citizen and insurer are selected
export const combinedSelection = derived(
  [selectedCitizenStore, selectedInsurerStore],
  ([$selectedCitizen, $selectedInsurer]) => {
    if ($selectedCitizen && $selectedInsurer) {
      return {
        citizenID: $selectedCitizen.citizenID,
        insurerCode: $selectedInsurer.insurerCode,
        displayName: `${$selectedCitizen.displayName} - ${$selectedInsurer.displayName}`
      };
    }
    return null;
  }
);

// Actions for member management

/**
 * Load member list
 * @param {boolean} forceRefresh - Force refresh from data source
 */
export async function loadMemberList(forceRefresh = false) {
  const currentState = get(memberListStore);

  // Check if we need to reload
  if (!forceRefresh && currentState.data && currentState.data.length > 0) {
    return currentState.data;
  }

  memberListStore.update(state => ({ ...state, loading: true, error: null }));

  try {
    // Simulate async loading (in real app, this would be an API call)
    await new Promise(resolve => setTimeout(resolve, 100));

    const members = await getAllMembers(false); // Only active members

    memberListStore.update(state => ({
      ...state,
      data: members,
      loading: false,
      error: null,
      lastUpdated: Date.now()
    }));

    return members;
  } catch (error) {
    console.error('Failed to load member list:', error);
    memberListStore.update(state => ({
      ...state,
      loading: false,
      error: error.message || 'Failed to load member list'
    }));
    throw error;
  }
}

/**
 * Select a member by member code
 * @param {string} memberCode - Member code to select
 */
export async function selectMember(memberCode) {
  if (!memberCode) {
    console.warn('Member code is required for selection');
    return;
  }

  try {
    const member = await getMemberByCode(memberCode);

    if (!member) {
      console.warn(`Member with code ${memberCode} not found`);
      return;
    }

    // Update the store
    selectedMemberStore.set(member);

    // Persist to session storage
    saveToSessionStorage(memberCode);

    console.log(`Selected member: ${getMemberDisplayName(member, 'TH')} (${memberCode})`);
  } catch (error) {
    console.error('Error selecting member:', error);
  }
}

/**
 * Clear member selection
 */
export function clearMemberSelection() {
  selectedMemberStore.set(null);
  saveToSessionStorage(null);
  console.log('Member selection cleared');
}

// Two-step selection actions

/**
 * Select a citizen (first step of two-step selection)
 * @param {string} citizenID - Citizen ID to select
 * @param {Object} citizenData - Citizen display data
 */
export function selectCitizen(citizenID, citizenData) {
  if (!citizenID || !citizenData) {
    console.warn('Citizen ID and data are required for selection');
    return;
  }

  // Update citizen store
  selectedCitizenStore.set(citizenData);

  // Clear insurer selection when citizen changes
  selectedInsurerStore.set(null);

  // Clear member selection until both citizen and insurer are selected
  selectedMemberStore.set(null);

  // Persist to session storage
  try {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(CITIZEN_STORAGE_KEY, JSON.stringify(citizenData));
      sessionStorage.removeItem(INSURER_STORAGE_KEY);
      sessionStorage.removeItem(STORAGE_KEY);
    }
  } catch (error) {
    console.warn('Failed to save citizen selection to session storage:', error);
  }

  console.log(`Selected citizen: ${citizenData.displayName} (${citizenID})`);
}

/**
 * Select an insurer (second step of two-step selection)
 * @param {string} insurerCode - Insurer code to select
 * @param {Object} insurerData - Insurer display data
 */
export async function selectInsurer(insurerCode, insurerData) {
  if (!insurerCode || !insurerData) {
    console.warn('Insurer code and data are required for selection');
    return;
  }

  const currentCitizen = get(selectedCitizenStore);
  if (!currentCitizen) {
    console.warn('No citizen selected. Please select a citizen first.');
    return;
  }

  // Update insurer store
  selectedInsurerStore.set(insurerData);

  try {
    // Find and set the corresponding member
    const member = await findMemberByCitizenAndInsurer(currentCitizen.citizenID, insurerCode);

    if (member) {
      selectedMemberStore.set(member);
      saveToSessionStorage(member.memberCode);
    }
  } catch (error) {
    console.error('Error finding member by citizen and insurer:', error);
  }

  // Persist insurer selection to session storage
  try {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(INSURER_STORAGE_KEY, JSON.stringify(insurerData));
    }
  } catch (error) {
    console.warn('Failed to save insurer selection to session storage:', error);
  }

  console.log(`Selected insurer: ${insurerData.displayName} (${insurerCode})`);
}

/**
 * Clear citizen selection (clears everything)
 */
export function clearCitizenSelection() {
  selectedCitizenStore.set(null);
  selectedInsurerStore.set(null);
  selectedMemberStore.set(null);

  try {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(CITIZEN_STORAGE_KEY);
      sessionStorage.removeItem(INSURER_STORAGE_KEY);
      sessionStorage.removeItem(STORAGE_KEY);
    }
  } catch (error) {
    console.warn('Failed to clear selections from session storage:', error);
  }

  console.log('All selections cleared');
}

/**
 * Clear insurer selection only
 */
export function clearInsurerSelection() {
  selectedInsurerStore.set(null);
  selectedMemberStore.set(null);

  try {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(INSURER_STORAGE_KEY);
      sessionStorage.removeItem(STORAGE_KEY);
    }
  } catch (error) {
    console.warn('Failed to clear insurer selection from session storage:', error);
  }

  console.log('Insurer selection cleared');
}

/**
 * Get current selected member (non-reactive)
 * @returns {Object|null} Current selected member
 */
export function getCurrentSelectedMember() {
  return get(selectedMemberStore);
}

/**
 * Check if a member is currently selected
 * @returns {boolean} Whether a member is selected
 */
export function hasMemberSelected() {
  const member = get(selectedMemberStore);
  return member !== null && member !== undefined;
}

/**
 * Initialize member store (call this on app startup)
 */
export async function initializeMemberStore() {
  try {
    // Load member list
    await loadMemberList();

    // Ensure we have a selected member
    const currentMember = get(selectedMemberStore);
    if (!currentMember) {
      const defaultMember = await getDefaultMember();
      if (defaultMember) {
        await selectMember(defaultMember.memberCode);
      }
    }

    console.log('Member store initialized successfully');
  } catch (error) {
    console.error('Failed to initialize member store:', error);
  }
}

/**
 * Refresh member data
 */
export async function refreshMemberData() {
  try {
    await loadMemberList(true);

    // Revalidate current selection
    const currentMember = get(selectedMemberStore);
    if (currentMember) {
      const updatedMember = await getMemberByCode(currentMember.memberCode);
      if (updatedMember) {
        selectedMemberStore.set(updatedMember);
      } else {
        // Member no longer exists, select default
        const defaultMember = await getDefaultMember();
        if (defaultMember) {
          await selectMember(defaultMember.memberCode);
        } else {
          clearMemberSelection();
        }
      }
    }

    console.log('Member data refreshed successfully');
  } catch (error) {
    console.error('Failed to refresh member data:', error);
    throw error;
  }
}

/**
 * Subscribe to member changes for debugging
 */
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  selectedMemberStore.subscribe(member => {
    console.log('Selected member changed:', member ? `${member.memberCode} - ${getMemberDisplayName(member, 'TH')}` : 'None');
  });
}
