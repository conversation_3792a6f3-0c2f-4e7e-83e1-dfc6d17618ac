/**
 * CSV Reader Utilities
 * 
 * Utilities for reading and parsing CSV data files containing member and insurer information.
 * Provides functions to load data from CSV files and convert them to structured objects.
 */

/**
 * Parse CSV content into array of objects
 * @param {string} csvContent - Raw CSV content
 * @returns {Array} Array of objects with CSV data
 */
function parseCSV(csvContent) {
  const lines = csvContent.trim().split('\n');
  if (lines.length < 2) {
    throw new Error('CSV file must contain at least a header row and one data row');
  }

  const headers = lines[0].split(',').map(header => header.trim());
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(value => value.trim());
    if (values.length !== headers.length) {
      console.warn(`Row ${i + 1} has ${values.length} columns, expected ${headers.length}. Skipping row.`);
      continue;
    }

    const row = {};
    headers.forEach((header, index) => {
      row[header] = values[index];
    });
    data.push(row);
  }

  return data;
}

/**
 * Load and parse CSV file
 * @param {string} filePath - Path to CSV file relative to public directory
 * @returns {Promise<Array>} Promise resolving to array of parsed CSV data
 */
async function loadCSV(filePath) {
  try {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`Failed to load CSV file: ${response.status} ${response.statusText}`);
    }

    const csvContent = await response.text();
    return parseCSV(csvContent);
  } catch (error) {
    console.error(`Error loading CSV file ${filePath}:`, error);
    throw new Error(`Failed to load CSV data from ${filePath}: ${error.message}`);
  }
}

/**
 * Cache for loaded CSV data to avoid repeated file reads
 */
const csvCache = new Map();

/**
 * Load CSV data with caching
 * @param {string} filePath - Path to CSV file
 * @param {boolean} forceReload - Whether to force reload from file
 * @returns {Promise<Array>} Promise resolving to cached or freshly loaded CSV data
 */
async function loadCSVCached(filePath, forceReload = false) {
  if (!forceReload && csvCache.has(filePath)) {
    return csvCache.get(filePath);
  }

  try {
    const data = await loadCSV(filePath);
    csvCache.set(filePath, data);
    return data;
  } catch (error) {
    // If we have cached data and loading fails, return cached data with warning
    if (csvCache.has(filePath)) {
      console.warn(`Failed to reload CSV ${filePath}, using cached data:`, error);
      return csvCache.get(filePath);
    }
    throw error;
  }
}

/**
 * Load member data from CSV
 * @param {boolean} forceReload - Whether to force reload from file
 * @returns {Promise<Array>} Promise resolving to array of member objects
 */
export async function loadMemberData(forceReload = false) {
  try {
    const data = await loadCSVCached('/data/members.csv', forceReload);

    // Validate required fields
    const requiredFields = ['memberCode', 'citizenID', 'insurerCode', 'titleTH', 'nameTH', 'surnameTH'];
    const validData = data.filter(row => {
      const hasRequiredFields = requiredFields.every(field => row[field] && row[field].trim() !== '');
      if (!hasRequiredFields) {
        console.warn('Skipping member row with missing required fields:', row);
      }
      return hasRequiredFields;
    });

    // If no valid data after filtering, return fallback
    if (validData.length === 0) {
      console.warn('No valid member data found, returning fallback data');
      return getFallbackMemberData();
    }

    return validData;
  } catch (error) {
    console.error('Error loading member data:', error);
    // Return fallback data if CSV loading fails
    return getFallbackMemberData();
  }
}

/**
 * Load insurer data from CSV
 * @param {boolean} forceReload - Whether to force reload from file
 * @returns {Promise<Array>} Promise resolving to array of insurer objects
 */
export async function loadInsurerData(forceReload = false) {
  try {
    const data = await loadCSVCached('/data/insurers.csv', forceReload);

    // Validate required fields
    const requiredFields = ['insurerCode', 'displayNameTH'];
    const validData = data.filter(row => {
      const hasRequiredFields = requiredFields.every(field => row[field] && row[field].trim() !== '');
      if (!hasRequiredFields) {
        console.warn('Skipping insurer row with missing required fields:', row);
      }
      return hasRequiredFields;
    });

    return validData;
  } catch (error) {
    console.error('Error loading insurer data:', error);
    // Return fallback data if CSV loading fails
    return getFallbackInsurerData();
  }
}

/**
 * Fallback member data in case CSV loading fails
 * @returns {Array} Array of fallback member objects
 */
function getFallbackMemberData() {
  return [
    {
      memberCode: 'MEM001',
      citizenID: '1234567890123',
      insurerCode: 'INS001',
      titleTH: 'นาย',
      nameTH: 'สมชาย',
      surnameTH: 'ใจดี',
      titleEN: 'Mr.',
      nameEN: 'Somchai',
      surnameEN: 'Jaidee',
      memberStatus: 'Active',
      memberType: 'Principal',
      vip: 'N',
      cardType: 'Standard',
      language: 'TH',
      citizenship: 'Thai',
      countryCode: 'TH'
    }
  ];
}

/**
 * Fallback insurer data in case CSV loading fails
 * @returns {Array} Array of fallback insurer objects
 */
function getFallbackInsurerData() {
  return [
    {
      insurerCode: 'INS001',
      displayNameTH: 'บริษัท ประกันภัย เอ จำกัด',
      displayNameEN: 'Insurance Company A Ltd.'
    }
  ];
}

/**
 * Clear CSV cache (useful for testing or forcing reload)
 */
export function clearCSVCache() {
  csvCache.clear();
}

/**
 * Get cache status for debugging
 * @returns {Object} Object with cache information
 */
export function getCSVCacheStatus() {
  return {
    size: csvCache.size,
    keys: Array.from(csvCache.keys())
  };
}
