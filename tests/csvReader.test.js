/**
 * CSV Reader Tests
 *
 * Tests for the CSV reading functionality including:
 * - Loading member data from CSV
 * - Loading insurer data from CSV
 * - Error handling for missing files
 * - Data validation and parsing
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { loadMemberData, loadInsurerData, clearCSVCache } from '../src/utils/csvReader.js';

// Mock fetch for testing
global.fetch = vi.fn();

describe('CSV Reader', () => {
  beforeEach(() => {
    // Clear cache before each test
    clearCSVCache();
    // Reset fetch mock
    fetch.mockReset();
  });

  describe('loadMemberData', () => {
    it('should load and parse member data from CSV', async () => {
      const mockCSV = `memberCode,citizenID,insurerCode,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,นาย,สมชาย,ใจดี,Mr<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,Principal,N,Standard,TH,Thai,TH
MEM002,1234567890124,INS002,นาง,มาลี,สวยงาม,Mrs.,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Active,Principal,N,Standard,TH,Thai,TH`;

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      const result = await loadMemberData();

      expect(fetch).toHaveBeenCalledWith('/data/members.csv');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        memberCode: 'MEM001',
        citizenID: '1234567890123',
        insurerCode: 'INS001',
        titleTH: 'นาย',
        nameTH: 'สมชาย',
        surnameTH: 'ใจดี',
        titleEN: 'Mr.',
        nameEN: 'Somchai',
        surnameEN: 'Jaidee',
        memberStatus: 'Active',
        memberType: 'Principal',
        vip: 'N',
        cardType: 'Standard',
        language: 'TH',
        citizenship: 'Thai',
        countryCode: 'TH'
      });
    });

    it('should handle fetch errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await loadMemberData();

      // Should return fallback data
      expect(result).toHaveLength(1);
      expect(result[0].memberCode).toBe('MEM001');
    });

    it('should handle invalid CSV format', async () => {
      const invalidCSV = 'invalid,csv,format\nwith,wrong,number,of,columns';

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(invalidCSV)
      });

      const result = await loadMemberData();

      // Should return fallback data when CSV is invalid
      expect(result).toHaveLength(1);
    });

    it('should filter out rows with missing required fields', async () => {
      const mockCSV = `memberCode,citizenID,insurerCode,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,Active,Principal,N,Standard,TH,Thai,TH
,1234567890124,INS002,นาง,มาลี,สวยงาม,Mrs.,Malee,Suayngam,Active,Principal,N,Standard,TH,Thai,TH
MEM003,,INS003,นางสาว,ปิยะดา,สุขใส,Ms.,Piyada,Suksai,Active,Principal,N,Standard,TH,Thai,TH`;

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      const result = await loadMemberData();

      // Should only return the valid row
      expect(result).toHaveLength(1);
      expect(result[0].memberCode).toBe('MEM001');
    });
  });

  describe('loadInsurerData', () => {
    it('should load and parse insurer data from CSV', async () => {
      const mockCSV = `insurerCode,displayNameTH,displayNameEN
INS001,บริษัท ประกันภัย เอ จำกัด,Insurance Company A Ltd.
INS002,บริษัท ประกันชีวิต แอล จำกัด,Life Insurance L Ltd.`;

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      const result = await loadInsurerData();

      expect(fetch).toHaveBeenCalledWith('/data/insurers.csv');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        insurerCode: 'INS001',
        displayNameTH: 'บริษัท ประกันภัย เอ จำกัด',
        displayNameEN: 'Insurance Company A Ltd.'
      });
    });

    it('should handle fetch errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await loadInsurerData();

      // Should return fallback data
      expect(result).toHaveLength(1);
      expect(result[0].insurerCode).toBe('INS001');
    });
  });

  describe('caching', () => {
    it('should cache loaded data', async () => {
      const mockCSV = `memberCode,citizenID,insurerCode,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,Active,Principal,N,Standard,TH,Thai,TH`;

      fetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      // First call
      await loadMemberData();
      
      // Second call should use cache
      await loadMemberData();

      // Fetch should only be called once
      expect(fetch).toHaveBeenCalledTimes(1);
    });

    it('should reload data when forceReload is true', async () => {
      const mockCSV = `memberCode,citizenID,insurerCode,titleTH,nameTH,surnameTH,titleEN,nameEN,surnameEN,memberStatus,memberType,vip,cardType,language,citizenship,countryCode
MEM001,1234567890123,INS001,นาย,สมชาย,ใจดี,Mr.,Somchai,Jaidee,Active,Principal,N,Standard,TH,Thai,TH`;

      fetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve(mockCSV)
      });

      // First call
      await loadMemberData();
      
      // Second call with forceReload
      await loadMemberData(true);

      // Fetch should be called twice
      expect(fetch).toHaveBeenCalledTimes(2);
    });
  });
});
